import React, { useState, useEffect } from "react";
import { Modal, List, ListItem, showToast } from "./ui-lib";
import { IconButton } from "./button";
import Locale from "../locales";
import styles from "./settings.module.scss";
import { useUserStore } from "../store/user";

interface ModelPricing {
  id: string;
  model: string;
  displayName: string;
  provider: string;
  providerDisplayName: string;
  inputTokenPrice: number;
  outputTokenPrice: number;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface ModelPricingManagerProps {
  onClose: () => void;
}

export function ModelPricingManager({ onClose }: ModelPricingManagerProps) {
  const [modelPricings, setModelPricings] = useState<ModelPricing[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingPricing, setEditingPricing] = useState<ModelPricing | null>(null);
  const userStore = useUserStore();

  // 加载模型定价数据
  const loadModelPricings = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/model-pricing", {
        headers: {
          "Authorization": `Bearer ${userStore.token}`,
        },
      });

      if (!response.ok) {
        throw new Error("获取模型定价失败");
      }

      const result = await response.json();
      if (result.success) {
        setModelPricings(result.data);
      } else {
        throw new Error(result.error || "获取模型定价失败");
      }
    } catch (error) {
      console.error("Load model pricings error:", error);
      showToast("获取模型定价失败");
    } finally {
      setLoading(false);
    }
  };

  // 保存模型定价
  const saveModelPricing = async (pricing: ModelPricing) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/admin/model-pricing/${pricing.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${userStore.token}`,
        },
        body: JSON.stringify({
          inputTokenPrice: pricing.inputTokenPrice,
          outputTokenPrice: pricing.outputTokenPrice,
          enabled: pricing.enabled,
          description: pricing.description,
        }),
      });

      if (!response.ok) {
        throw new Error("更新模型定价失败");
      }

      const result = await response.json();
      if (result.success) {
        showToast("模型定价已更新");
        await loadModelPricings(); // 重新加载数据
        setEditingPricing(null);
      } else {
        throw new Error(result.error || "更新模型定价失败");
      }
    } catch (error) {
      console.error("Save model pricing error:", error);
      showToast("更新模型定价失败");
    } finally {
      setSaving(false);
    }
  };

  // 按服务商分组模型
  const groupedPricings = modelPricings.reduce((groups, pricing) => {
    const provider = pricing.providerDisplayName;
    if (!groups[provider]) {
      groups[provider] = [];
    }
    groups[provider].push(pricing);
    return groups;
  }, {} as Record<string, ModelPricing[]>);

  useEffect(() => {
    loadModelPricings();
  }, []);

  return (
    <div className="modal-mask">
      <Modal
        title="模型定价管理"
        onClose={onClose}
        actions={[
          <IconButton
            key="close"
            text="关闭"
            onClick={onClose}
          />,
        ]}
      >
        <div className={styles["model-pricing-manager"]}>
          {loading ? (
            <div className={styles["loading"]}>加载中...</div>
          ) : (
            <div className={styles["pricing-list"]}>
              {Object.entries(groupedPricings).map(([provider, pricings]) => (
                <div key={provider} className={styles["provider-group"]}>
                  <h3 className={styles["provider-title"]}>{provider}</h3>
                  <List>
                    {pricings.map((pricing) => (
                      <ListItem
                        key={pricing.id}
                        title={pricing.displayName}
                        subTitle={`输入: $${pricing.inputTokenPrice.toFixed(6)}/1K tokens | 输出: $${pricing.outputTokenPrice.toFixed(6)}/1K tokens`}
                      >
                        <div className={styles["pricing-actions"]}>
                          <IconButton
                            text="编辑"
                            onClick={() => setEditingPricing(pricing)}
                          />
                          <div className={styles["pricing-status"]}>
                            {pricing.enabled ? "✅" : "❌"}
                          </div>
                        </div>
                      </ListItem>
                    ))}
                  </List>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 编辑定价弹窗 */}
        {editingPricing && (
          <PricingEditModal
            pricing={editingPricing}
            onSave={saveModelPricing}
            onCancel={() => setEditingPricing(null)}
            saving={saving}
          />
        )}
      </Modal>
    </div>
  );
}

interface PricingEditModalProps {
  pricing: ModelPricing;
  onSave: (pricing: ModelPricing) => void;
  onCancel: () => void;
  saving: boolean;
}

function PricingEditModal({ pricing, onSave, onCancel, saving }: PricingEditModalProps) {
  const [editedPricing, setEditedPricing] = useState<ModelPricing>({ ...pricing });

  const handleSave = () => {
    onSave(editedPricing);
  };

  return (
    <div className="modal-mask">
      <Modal
        title={`编辑 ${editedPricing.displayName} 定价`}
        onClose={onCancel}
        actions={[
          <IconButton
            key="cancel"
            text="取消"
            onClick={onCancel}
          />,
          <IconButton
            key="save"
            text={saving ? "保存中..." : "保存"}
            type="primary"
            onClick={handleSave}
            disabled={saving}
          />,
        ]}
      >
        <div className={styles["pricing-edit-form"]}>
          <List>
            <ListItem title="模型名称">
              <span>{editedPricing.displayName}</span>
            </ListItem>
            <ListItem title="服务商">
              <span>{editedPricing.providerDisplayName}</span>
            </ListItem>
            <ListItem title="输入Token价格 ($/1K tokens)">
              <input
                type="number"
                step="0.000001"
                min="0"
                value={editedPricing.inputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    inputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
              />
            </ListItem>
            <ListItem title="输出Token价格 ($/1K tokens)">
              <input
                type="number"
                step="0.000001"
                min="0"
                value={editedPricing.outputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    outputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
              />
            </ListItem>
            <ListItem title="启用状态">
              <input
                type="checkbox"
                checked={editedPricing.enabled}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    enabled: e.target.checked,
                  })
                }
              />
            </ListItem>
            <ListItem title="描述">
              <input
                type="text"
                value={editedPricing.description || ""}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    description: e.target.value,
                  })
                }
                placeholder="可选的描述信息"
              />
            </ListItem>
          </List>
        </div>
      </Modal>
    </div>
  );
}
