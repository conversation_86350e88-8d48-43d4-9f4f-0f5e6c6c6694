.model-pricing-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: var(--border-in-light);

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  p {
    font-size: 14px;
    color: var(--gray);
    margin: 0;
    line-height: 1.4;
  }
}

.content {
  flex: 1;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-text {
  font-size: 16px;
  color: var(--gray);
}

.provider-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.provider-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 16px 0;
  padding: 8px 12px;
  background: var(--second);
  border-radius: 8px;
  border: var(--border-in-light);
}

.pricing-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 13px;
  color: var(--gray);

  span {
    line-height: 1.3;
  }
}

.status {
  font-weight: 500;
  
  &:contains("✅") {
    color: var(--green);
  }
  
  &:contains("❌") {
    color: var(--red);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--gray);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: var(--gray);
  margin: 0;
  opacity: 0.8;
}

// 编辑弹窗样式
.edit-modal {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: var(--border-in-light);

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
  }
}

.modal-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.modal-actions {
  padding: 16px 24px 20px;
  border-top: var(--border-in-light);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 输入框样式
.price-input,
.text-input {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 6px;
  background: var(--white);
  color: var(--black);
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: var(--primary);
  }

  &:disabled {
    background: var(--second);
    color: var(--gray);
    cursor: not-allowed;
  }
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary);
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    h2 {
      font-size: 20px;
    }
  }

  .provider-title {
    font-size: 16px;
  }

  .edit-modal {
    width: 95%;
    max-height: 90vh;
  }

  .modal-header,
  .modal-content,
  .modal-actions {
    padding-left: 16px;
    padding-right: 16px;
  }

  .price-input,
  .text-input {
    width: 100%;
    max-width: 200px;
  }
}

// 暗色主题适配
.dark {
  .edit-modal {
    background: var(--white);
  }

  .modal-header h3 {
    color: var(--black);
  }

  .price-input,
  .text-input {
    background: var(--white);
    color: var(--black);
    border-color: var(--border-in-light);

    &:focus {
      border-color: var(--primary);
    }

    &:disabled {
      background: var(--second);
      color: var(--gray);
    }
  }
}
