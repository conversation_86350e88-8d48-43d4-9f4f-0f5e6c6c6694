import { prisma } from "./prisma";

export interface ApiLogData {
  userId?: string;
  providerId: string;
  model: string;
  endpoint: string;
  method: string;
  tokensUsed?: number;
  cost?: number;
  status: "success" | "error" | "timeout";
  errorMsg?: string;
  duration?: number;
  // 新增字段
  promptTokens?: number;
  completionTokens?: number;
  requestBody?: string; // JSON字符串，用于调试
  responseBody?: string; // JSON字符串，用于调试（仅保存关键信息）
}

export interface ApiLogQuery {
  userId?: string;
  providerId?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

// 模型定价配置（每1000个token的价格，单位：美元）
export const MODEL_PRICING: Record<string, { input: number; output: number }> =
  {
    // OpenAI GPT-4 系列
    "gpt-4": { input: 0.03, output: 0.06 },
    "gpt-4.1": { input: 0.03, output: 0.06 }, // 添加 gpt-4.1 定价
    "gpt-4-turbo": { input: 0.01, output: 0.03 },
    "gpt-4-turbo-preview": { input: 0.01, output: 0.03 },
    "gpt-4-0125-preview": { input: 0.01, output: 0.03 },
    "gpt-4-1106-preview": { input: 0.01, output: 0.03 },
    "gpt-4-vision-preview": { input: 0.01, output: 0.03 },

    // OpenAI GPT-3.5 系列
    "gpt-3.5-turbo": { input: 0.0015, output: 0.002 },
    "gpt-3.5-turbo-0125": { input: 0.0005, output: 0.0015 },
    "gpt-3.5-turbo-instruct": { input: 0.0015, output: 0.002 },

    // Claude 系列
    "claude-3-opus-20240229": { input: 0.015, output: 0.075 },
    "claude-3-sonnet-20240229": { input: 0.003, output: 0.015 },
    "claude-3-haiku-20240307": { input: 0.00025, output: 0.00125 },
    "claude-3-5-sonnet-20241022": { input: 0.003, output: 0.015 },

    // Google Gemini 系列
    "gemini-pro": { input: 0.0005, output: 0.0015 },
    "gemini-pro-vision": { input: 0.0005, output: 0.0015 },
    "gemini-1.5-pro": { input: 0.0035, output: 0.0105 },
    "gemini-1.5-flash": { input: 0.00035, output: 0.00105 },

    // 其他模型的默认定价
    default: { input: 0.001, output: 0.002 },
  };

/**
 * 从请求体中提取模型名称
 */
export function extractModelFromRequest(requestBody: any): string {
  if (!requestBody) return "unknown";

  // 处理不同的请求格式
  if (typeof requestBody === "string") {
    try {
      requestBody = JSON.parse(requestBody);
    } catch {
      return "unknown";
    }
  }

  return requestBody.model || "unknown";
}

/**
 * 从响应体中提取token使用量信息
 */
export function extractTokenUsageFromResponse(responseBody: any): {
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
} {
  if (!responseBody) {
    console.log("[Token Extract] No response body");
    return {};
  }

  // 处理不同的响应格式
  if (typeof responseBody === "string") {
    try {
      responseBody = JSON.parse(responseBody);
    } catch {
      console.log("[Token Extract] Failed to parse response body as JSON");
      return {};
    }
  }

  // OpenAI格式
  if (responseBody.usage && responseBody.usage.prompt_tokens !== undefined) {
    console.log(
      "[Token Extract] Found OpenAI format usage:",
      responseBody.usage,
    );
    return {
      promptTokens: responseBody.usage.prompt_tokens,
      completionTokens: responseBody.usage.completion_tokens,
      totalTokens: responseBody.usage.total_tokens,
    };
  }

  // Claude格式
  if (responseBody.usage && responseBody.usage.input_tokens !== undefined) {
    console.log(
      "[Token Extract] Found Claude format usage:",
      responseBody.usage,
    );
    return {
      promptTokens: responseBody.usage.input_tokens,
      completionTokens: responseBody.usage.output_tokens,
      totalTokens:
        (responseBody.usage.input_tokens || 0) +
        (responseBody.usage.output_tokens || 0),
    };
  }

  // Google Gemini格式
  if (responseBody.usageMetadata) {
    console.log(
      "[Token Extract] Found Gemini format usage:",
      responseBody.usageMetadata,
    );
    return {
      promptTokens: responseBody.usageMetadata.promptTokenCount,
      completionTokens: responseBody.usageMetadata.candidatesTokenCount,
      totalTokens: responseBody.usageMetadata.totalTokenCount,
    };
  }

  console.log(
    "[Token Extract] No matching format found, response keys:",
    Object.keys(responseBody),
  );
  return {};
}

/**
 * 计算API调用成本（使用数据库定价）
 */
export async function calculateCostFromDB(
  model: string,
  promptTokens: number = 0,
  completionTokens: number = 0,
): Promise<number> {
  try {
    const pricing = await prisma.modelPricing.findUnique({
      where: { model },
    });

    if (!pricing) {
      // 如果没有找到定价，使用默认定价
      const fallbackPricing = MODEL_PRICING[model] || MODEL_PRICING["default"];
      const inputCost = (promptTokens / 1000) * fallbackPricing.input;
      const outputCost = (completionTokens / 1000) * fallbackPricing.output;
      return inputCost + outputCost;
    }

    const inputCost = (promptTokens / 1000) * pricing.inputTokenPrice;
    const outputCost = (completionTokens / 1000) * pricing.outputTokenPrice;

    return inputCost + outputCost;
  } catch (error) {
    console.error("计算成本失败:", error);
    // 回退到原有的计算方式
    return calculateCost(model, promptTokens, completionTokens);
  }
}

/**
 * 计算API调用成本（同步版本，使用内存定价）
 * 注意：这个函数保留用于向后兼容，新代码应该使用 calculateCostFromDB
 */
export function calculateCost(
  model: string,
  promptTokens: number = 0,
  completionTokens: number = 0,
): number {
  const pricing = MODEL_PRICING[model] || MODEL_PRICING["default"];

  console.log(
    `[Cost Calc] Model: ${model}, Pricing: ${JSON.stringify(
      pricing,
    )}, Tokens: ${promptTokens}/${completionTokens}`,
  );

  const inputCost = (promptTokens / 1000) * pricing.input;
  const outputCost = (completionTokens / 1000) * pricing.output;
  const totalCost = inputCost + outputCost;

  console.log(
    `[Cost Calc] Input: $${inputCost.toFixed(6)}, Output: $${outputCost.toFixed(
      6,
    )}, Total: $${totalCost.toFixed(6)}`,
  );

  return totalCost;
}

/**
 * 智能计算API调用成本（优先使用数据库定价，失败时回退到内存定价）
 * 这是推荐的成本计算函数
 */
export async function calculateCostSmart(
  model: string,
  promptTokens: number = 0,
  completionTokens: number = 0,
): Promise<number> {
  try {
    // 首先尝试从数据库获取定价
    return await calculateCostFromDB(model, promptTokens, completionTokens);
  } catch (error) {
    console.warn(`[Cost Calc Smart] Database pricing failed for ${model}, falling back to memory pricing:`, error);
    // 回退到内存定价
    return calculateCost(model, promptTokens, completionTokens);
  }
}

/**
 * 记录API调用日志
 */
export async function logApiCall(data: ApiLogData): Promise<void> {
  try {
    await prisma.apiLog.create({
      data: {
        userId: data.userId || null,
        providerId: data.providerId,
        model: data.model,
        endpoint: data.endpoint,
        method: data.method,
        tokensUsed: data.tokensUsed || null,
        promptTokens: data.promptTokens || null,
        completionTokens: data.completionTokens || null,
        cost: data.cost || null,
        status: data.status,
        errorMsg: data.errorMsg || null,
        duration: data.duration || null,
        requestBody: data.requestBody || null,
        responseBody: data.responseBody || null,
      },
    });

    // 如果调用成功且有用户ID，更新用户统计
    if (data.status === "success" && data.userId) {
      await updateUserStats(data.userId, {
        tokensUsed: data.tokensUsed || 0,
        promptTokens: data.promptTokens || 0,
        completionTokens: data.completionTokens || 0,
        cost: data.cost || 0,
      });
    }

    // 更新系统统计
    await updateSystemStats({
      apiCalls: 1,
      successCalls: data.status === "success" ? 1 : 0,
      tokens: data.tokensUsed || 0,
      cost: data.cost || 0,
    });
  } catch (error) {
    console.error("Failed to log API call:", error);
    // 不抛出错误，避免影响主要业务流程
  }
}

/**
 * 增强的API调用日志记录函数
 * 自动提取模型信息、token使用量和计算成本
 */
export async function logEnhancedApiCall(params: {
  userId?: string;
  providerId: string;
  endpoint: string;
  method: string;
  requestBody?: any;
  responseBody?: any;
  status: "success" | "error" | "timeout";
  errorMsg?: string;
  duration?: number;
}): Promise<void> {
  try {
    const {
      userId,
      providerId,
      endpoint,
      method,
      requestBody,
      responseBody,
      status,
      errorMsg,
      duration,
    } = params;

    // 提取模型名称
    const model = extractModelFromRequest(requestBody);

    // 提取token使用量
    const tokenUsage = extractTokenUsageFromResponse(responseBody);

    // 调试日志
    const isStreamResponse = responseBody === null;
    console.log(
      `[API Log Debug] Model: ${model}, TokenUsage:`,
      tokenUsage,
      "ResponseBody keys:",
      responseBody ? Object.keys(responseBody) : "null",
      "IsStream:",
      isStreamResponse,
    );

    // 计算成本 - 优先使用数据库定价
    const cost = await calculateCostFromDB(
      model,
      tokenUsage.promptTokens || 0,
      tokenUsage.completionTokens || 0,
    );

    // 准备调试信息（只保存关键信息，避免存储过多数据）
    const requestDebugInfo = requestBody
      ? {
          model: extractModelFromRequest(requestBody),
          messages: requestBody.messages
            ? `${requestBody.messages.length} messages`
            : undefined,
          max_tokens: requestBody.max_tokens,
          temperature: requestBody.temperature,
          stream: requestBody.stream,
        }
      : undefined;

    const responseDebugInfo =
      responseBody && status === "success"
        ? {
            usage: tokenUsage,
            model: responseBody.model,
            finish_reason: responseBody.choices?.[0]?.finish_reason,
          }
        : undefined;

    // 记录API调用日志
    await logApiCall({
      userId,
      providerId,
      model,
      endpoint,
      method,
      tokensUsed: tokenUsage.totalTokens || undefined,
      promptTokens: tokenUsage.promptTokens || undefined,
      completionTokens: tokenUsage.completionTokens || undefined,
      cost: tokenUsage.totalTokens ? cost : undefined, // 只有有Token数据时才计算成本
      status,
      errorMsg,
      duration,
      requestBody: requestDebugInfo
        ? JSON.stringify(requestDebugInfo)
        : undefined,
      responseBody: responseDebugInfo
        ? JSON.stringify(responseDebugInfo)
        : undefined,
    });

    console.log(
      `[API Log] ${status.toUpperCase()} - ${model} - ${
        tokenUsage.totalTokens || 0
      } tokens - $${cost?.toFixed(6) || 0}`,
    );
  } catch (error) {
    console.error("Failed to log enhanced API call:", error);
  }
}

/**
 * 查询API调用日志
 */
export async function getApiLogs(query: ApiLogQuery = {}) {
  const where: any = {};

  if (query.userId) where.userId = query.userId;
  if (query.providerId) where.providerId = query.providerId;
  if (query.status) where.status = query.status;
  if (query.startDate || query.endDate) {
    where.createdAt = {};
    if (query.startDate) where.createdAt.gte = query.startDate;
    if (query.endDate) where.createdAt.lte = query.endDate;
  }

  const logs = await prisma.apiLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
      provider: {
        select: {
          id: true,
          name: true,
          type: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
    take: query.limit || 100,
    skip: query.offset || 0,
  });

  return logs;
}

/**
 * 获取API调用统计
 */
export async function getApiStats(
  query: Omit<ApiLogQuery, "limit" | "offset"> = {},
) {
  const where: any = {};

  if (query.userId) where.userId = query.userId;
  if (query.providerId) where.providerId = query.providerId;
  if (query.status) where.status = query.status;
  if (query.startDate || query.endDate) {
    where.createdAt = {};
    if (query.startDate) where.createdAt.gte = query.startDate;
    if (query.endDate) where.createdAt.lte = query.endDate;
  }

  const [totalCalls, successCalls, errorCalls, totalTokens, totalCost] =
    await Promise.all([
      // 总调用次数
      prisma.apiLog.count({ where }),

      // 成功调用次数
      prisma.apiLog.count({
        where: { ...where, status: "success" },
      }),

      // 错误调用次数
      prisma.apiLog.count({
        where: { ...where, status: "error" },
      }),

      // 总token使用量
      prisma.apiLog.aggregate({
        where,
        _sum: { tokensUsed: true },
      }),

      // 总成本
      prisma.apiLog.aggregate({
        where,
        _sum: { cost: true },
      }),
    ]);

  return {
    totalCalls,
    successCalls,
    errorCalls,
    successRate:
      totalCalls > 0 ? ((successCalls / totalCalls) * 100).toFixed(2) : "0",
    totalTokens: totalTokens._sum.tokensUsed || 0,
    totalCost: totalCost._sum.cost || 0,
  };
}

/**
 * 获取用户API使用统计
 */
export async function getUserApiStats(userId: string, days: number = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return getApiStats({
    userId,
    startDate,
  });
}

/**
 * 获取服务商API使用统计
 */
export async function getProviderApiStats(
  providerId: string,
  days: number = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return getApiStats({
    providerId,
    startDate,
  });
}

/**
 * 更新用户统计
 */
async function updateUserStats(
  userId: string,
  stats: {
    tokensUsed: number;
    promptTokens: number;
    completionTokens: number;
    cost: number;
  },
): Promise<void> {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 更新或创建今日统计
    await prisma.userStats.upsert({
      where: {
        userId_date: {
          userId,
          date: today,
        },
      },
      update: {
        tokensUsed: { increment: stats.tokensUsed },
        promptTokens: { increment: stats.promptTokens },
        completionTokens: { increment: stats.completionTokens },
        cost: { increment: stats.cost },
        apiCalls: { increment: 1 },
        successCalls: { increment: 1 },
      },
      create: {
        userId,
        date: today,
        tokensUsed: stats.tokensUsed,
        promptTokens: stats.promptTokens,
        completionTokens: stats.completionTokens,
        cost: stats.cost,
        apiCalls: 1,
        successCalls: 1,
        errorCalls: 0,
      },
    });

    // 更新用户总统计
    await prisma.user.update({
      where: { id: userId },
      data: {
        totalTokensUsed: { increment: stats.tokensUsed },
        totalCost: { increment: stats.cost },
        monthlyTokensUsed: { increment: stats.tokensUsed },
        monthlyCost: { increment: stats.cost },
      },
    });
  } catch (error) {
    console.error("更新用户统计失败:", error);
  }
}

/**
 * 更新系统统计
 */
async function updateSystemStats(stats: {
  apiCalls: number;
  successCalls: number;
  tokens: number;
  cost: number;
}): Promise<void> {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await prisma.systemStats.upsert({
      where: { date: today },
      update: {
        totalApiCalls: { increment: stats.apiCalls },
        successApiCalls: { increment: stats.successCalls },
        totalTokens: { increment: stats.tokens },
        totalCost: { increment: stats.cost },
      },
      create: {
        date: today,
        totalUsers: 0, // 这个需要定期更新
        activeUsers: 0, // 这个需要定期更新
        totalApiCalls: stats.apiCalls,
        successApiCalls: stats.successCalls,
        totalTokens: stats.tokens,
        totalCost: stats.cost,
        totalRevenue: 0,
      },
    });
  } catch (error) {
    console.error("更新系统统计失败:", error);
  }
}

/**
 * 清理过期日志
 */
export async function cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await prisma.apiLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate,
      },
    },
  });

  return result.count;
}
